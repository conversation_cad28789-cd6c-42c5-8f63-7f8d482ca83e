#!/usr/bin/env python3
"""
Solar System Simulation
A comprehensive 2D solar system simulation using pygame with realistic orbital mechanics.

Features:
- Object-oriented design with celestial body classes
- Realistic orbital motion based on <PERSON><PERSON>'s laws
- Interactive controls (pause, speed adjustment, planet information)
- Visual enhancements (trails, labels, scaling)
- Smooth 60 FPS animation

Author: AI Assistant
Date: 2025-08-05
"""

import pygame
import math
import sys
import random
import time
from typing import List, Tuple, Optional

# Initialize pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1400
SCREEN_HEIGHT = 900
FPS = 60
BACKGROUND_COLOR = (2, 2, 8)  # Deeper space black
WHITE = (255, 255, 255)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)
GOLD = (255, 215, 0)
SILVER = (192, 192, 192)
CYAN = (0, 255, 255)

# Enhanced color palette
COLORS = {
    'space_blue': (10, 15, 35),
    'nebula_purple': (75, 0, 130),
    'star_white': (255, 255, 240),
    'ui_dark': (25, 25, 45),
    'ui_light': (60, 60, 100),
    'accent_blue': (100, 150, 255),
    'accent_gold': (255, 200, 50)
}

# Physics constants (scaled for visualization)
AU = 120  # Astronomical Unit in pixels (scaled down from real 149.6M km)
GRAVITATIONAL_CONSTANT = 1.0  # Simplified for simulation
SUN_MASS = 1000  # Arbitrary units for simulation


class Particle:
    """Particle system for visual effects."""

    def __init__(self, x: float, y: float, vx: float, vy: float,
                 color: Tuple[int, int, int], life: float, size: float = 1.0):
        self.x = x
        self.y = y
        self.vx = vx
        self.vy = vy
        self.color = color
        self.life = life
        self.max_life = life
        self.size = size
        self.alpha = 255

    def update(self, dt: float = 1.0):
        """Update particle position and life."""
        self.x += self.vx * dt
        self.y += self.vy * dt
        self.life -= dt

        # Fade out over time
        life_ratio = max(0, self.life / self.max_life)
        self.alpha = int(255 * life_ratio)

        return self.life > 0

    def draw(self, screen: pygame.Surface, camera_offset: Tuple[float, float] = (0, 0)):
        """Draw the particle."""
        if self.alpha <= 0:
            return

        screen_x = int(self.x - camera_offset[0])
        screen_y = int(self.y - camera_offset[1])

        if 0 <= screen_x <= SCREEN_WIDTH and 0 <= screen_y <= SCREEN_HEIGHT:
            # Create surface with alpha for transparency
            particle_surface = pygame.Surface((self.size * 2, self.size * 2), pygame.SRCALPHA)
            color_with_alpha = (*self.color, self.alpha)
            pygame.draw.circle(particle_surface, color_with_alpha,
                             (int(self.size), int(self.size)), int(self.size))
            screen.blit(particle_surface, (screen_x - self.size, screen_y - self.size))


class ParticleSystem:
    """Manages multiple particles for effects."""

    def __init__(self):
        self.particles = []

    def add_particle(self, particle: Particle):
        """Add a particle to the system."""
        self.particles.append(particle)

    def add_explosion(self, x: float, y: float, color: Tuple[int, int, int], count: int = 20):
        """Create an explosion effect."""
        for _ in range(count):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(1, 5)
            vx = math.cos(angle) * speed
            vy = math.sin(angle) * speed
            life = random.uniform(30, 60)
            size = random.uniform(1, 3)

            particle = Particle(x, y, vx, vy, color, life, size)
            self.add_particle(particle)

    def add_solar_wind(self, sun_x: float, sun_y: float):
        """Create solar wind particles."""
        for _ in range(5):
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(25, 40)
            x = sun_x + math.cos(angle) * distance
            y = sun_y + math.sin(angle) * distance

            speed = random.uniform(0.5, 2.0)
            vx = math.cos(angle) * speed
            vy = math.sin(angle) * speed

            # Golden solar wind particles
            color = (255, random.randint(200, 255), random.randint(100, 200))
            life = random.uniform(100, 200)
            size = random.uniform(0.5, 1.5)

            particle = Particle(x, y, vx, vy, color, life, size)
            self.add_particle(particle)

    def update(self, dt: float = 1.0):
        """Update all particles."""
        self.particles = [p for p in self.particles if p.update(dt)]

    def draw(self, screen: pygame.Surface, camera_offset: Tuple[float, float] = (0, 0)):
        """Draw all particles."""
        for particle in self.particles:
            particle.draw(screen, camera_offset)


class CelestialBody:
    """Base class for all celestial objects in the solar system."""

    def __init__(self, name: str, x: float, y: float, radius: float,
                 color: Tuple[int, int, int], mass: float = 1.0):
        """
        Initialize a celestial body.

        Args:
            name: Name of the celestial body
            x, y: Initial position coordinates
            radius: Visual radius in pixels
            color: RGB color tuple
            mass: Mass for gravitational calculations
        """
        self.name = name
        self.x = x
        self.y = y
        self.radius = radius
        self.color = color
        self.mass = mass
        self.trail = []  # Store previous positions for orbital trails
        self.max_trail_length = 300  # Longer trails for beauty
        self.rotation_angle = 0.0  # For planet rotation
        self.glow_intensity = 0.0  # For atmospheric glow
        self.pulse_phase = random.uniform(0, 2 * math.pi)  # For pulsing effects
        
    def draw(self, screen: pygame.Surface, camera_offset: Tuple[float, float] = (0, 0)):
        """Draw the celestial body with enhanced visual effects."""
        screen_x = int(self.x - camera_offset[0])
        screen_y = int(self.y - camera_offset[1])

        # Draw orbital trail with gradient effect
        if len(self.trail) > 1:
            trail_points = []
            for i, (tx, ty) in enumerate(self.trail):
                trail_x = int(tx - camera_offset[0])
                trail_y = int(ty - camera_offset[1])
                if -50 <= trail_x <= SCREEN_WIDTH + 50 and -50 <= trail_y <= SCREEN_HEIGHT + 50:
                    trail_points.append((trail_x, trail_y))

            if len(trail_points) > 1:
                # Draw trail with beautiful fading effect
                for i in range(1, len(trail_points)):
                    alpha_ratio = i / len(trail_points)
                    alpha = int(255 * alpha_ratio * 0.6)
                    width = max(1, int(3 * alpha_ratio))

                    # Create gradient color
                    fade_color = tuple(int(c * alpha_ratio + 50 * (1 - alpha_ratio)) for c in self.color)

                    if i < len(trail_points):
                        pygame.draw.line(screen, fade_color, trail_points[i-1], trail_points[i], width)

        # Draw atmospheric glow for planets
        if hasattr(self, 'glow_intensity') and self.glow_intensity > 0:
            glow_radius = self.radius + 8
            glow_alpha = int(self.glow_intensity * 100)
            glow_surface = pygame.Surface((glow_radius * 2, glow_radius * 2), pygame.SRCALPHA)
            glow_color = (*self.color, glow_alpha)
            pygame.draw.circle(glow_surface, glow_color, (glow_radius, glow_radius), glow_radius)
            screen.blit(glow_surface, (screen_x - glow_radius, screen_y - glow_radius))

        # Draw the main celestial body with enhanced shading
        self._draw_enhanced_body(screen, screen_x, screen_y)

        # Draw name label with better styling
        self._draw_enhanced_label(screen, screen_x, screen_y)

    def _draw_enhanced_body(self, screen: pygame.Surface, screen_x: int, screen_y: int):
        """Draw the celestial body with enhanced 3D-like shading."""
        # Main body
        pygame.draw.circle(screen, self.color, (screen_x, screen_y), self.radius)

        # Add highlight for 3D effect
        highlight_offset = self.radius // 3
        highlight_color = tuple(min(255, c + 60) for c in self.color)
        highlight_radius = self.radius // 2
        pygame.draw.circle(screen, highlight_color,
                         (screen_x - highlight_offset, screen_y - highlight_offset),
                         highlight_radius)

        # Add subtle shadow
        shadow_color = tuple(max(0, c - 40) for c in self.color)
        shadow_radius = self.radius // 3
        pygame.draw.circle(screen, shadow_color,
                         (screen_x + highlight_offset, screen_y + highlight_offset),
                         shadow_radius)

    def _draw_enhanced_label(self, screen: pygame.Surface, screen_x: int, screen_y: int):
        """Draw enhanced name label with background."""
        font = pygame.font.Font(None, 24)
        text = font.render(self.name, True, WHITE)
        text_rect = text.get_rect(center=(screen_x, screen_y + self.radius + 20))

        # Draw label background
        bg_rect = text_rect.inflate(10, 4)
        bg_surface = pygame.Surface(bg_rect.size, pygame.SRCALPHA)
        pygame.draw.rect(bg_surface, (0, 0, 0, 120), bg_surface.get_rect(), border_radius=5)
        screen.blit(bg_surface, bg_rect)

        # Draw text
        screen.blit(text, text_rect)
    
    def update_trail(self):
        """Update the orbital trail."""
        self.trail.append((self.x, self.y))
        if len(self.trail) > self.max_trail_length:
            self.trail.pop(0)


class Sun(CelestialBody):
    """The Sun - central star of the solar system with enhanced visual effects."""

    def __init__(self, x: float, y: float):
        super().__init__("Sun", x, y, radius=25, color=(255, 220, 0), mass=SUN_MASS)
        self.is_star = True
        self.corona_particles = []
        self.flare_intensity = 0.0
        self.pulse_timer = 0.0

    def update(self, dt: float = 1.0):
        """Update sun effects."""
        self.pulse_timer += dt * 0.05
        self.flare_intensity = 0.3 + 0.2 * math.sin(self.pulse_timer)

        # Update corona particles
        self.corona_particles = [p for p in self.corona_particles if p.update(dt)]

        # Add new corona particles
        if len(self.corona_particles) < 50:
            self._add_corona_particle()

    def _add_corona_particle(self):
        """Add a corona particle around the sun."""
        angle = random.uniform(0, 2 * math.pi)
        distance = random.uniform(self.radius + 5, self.radius + 15)
        x = self.x + math.cos(angle) * distance
        y = self.y + math.sin(angle) * distance

        vx = random.uniform(-0.5, 0.5)
        vy = random.uniform(-0.5, 0.5)

        color = (255, random.randint(180, 255), random.randint(50, 150))
        life = random.uniform(60, 120)
        size = random.uniform(1, 2)

        particle = Particle(x, y, vx, vy, color, life, size)
        self.corona_particles.append(particle)

    def draw(self, screen: pygame.Surface, camera_offset: Tuple[float, float] = (0, 0)):
        """Draw the Sun with spectacular effects."""
        screen_x = int(self.x - camera_offset[0])
        screen_y = int(self.y - camera_offset[1])

        # Draw corona particles
        for particle in self.corona_particles:
            particle.draw(screen, camera_offset)

        # Draw multiple glow layers with pulsing effect
        pulse_factor = 1.0 + 0.1 * math.sin(self.pulse_timer * 2)

        for i in range(8):
            glow_radius = int((self.radius + i * 4) * pulse_factor)
            glow_alpha = max(0, int((150 - i * 15) * self.flare_intensity))

            # Create gradient colors from yellow to orange to red
            if i < 3:
                glow_color = (255, 255 - i * 30, 100 - i * 30, glow_alpha)
            elif i < 6:
                glow_color = (255, 200 - i * 20, 50, glow_alpha)
            else:
                glow_color = (255, 150, 0, glow_alpha)

            glow_surface = pygame.Surface((glow_radius * 2, glow_radius * 2), pygame.SRCALPHA)
            pygame.draw.circle(glow_surface, glow_color, (glow_radius, glow_radius), glow_radius)
            screen.blit(glow_surface, (screen_x - glow_radius, screen_y - glow_radius))

        # Draw the Sun core with gradient
        core_colors = [
            (255, 255, 200),  # Bright center
            (255, 240, 150),  # Mid layer
            (255, 220, 100),  # Outer layer
        ]

        for i, color in enumerate(core_colors):
            radius = self.radius - i * 3
            if radius > 0:
                pygame.draw.circle(screen, color, (screen_x, screen_y), radius)

        # Draw solar flares occasionally
        if random.random() < 0.02:  # 2% chance per frame
            self._draw_solar_flare(screen, screen_x, screen_y)

        # Enhanced name label
        font = pygame.font.Font(None, 28)
        text = font.render(self.name, True, GOLD)
        text_rect = text.get_rect(center=(screen_x, screen_y + self.radius + 30))

        # Glowing text effect
        glow_text = font.render(self.name, True, (255, 255, 150))
        for dx, dy in [(-1, -1), (-1, 1), (1, -1), (1, 1)]:
            screen.blit(glow_text, (text_rect.x + dx, text_rect.y + dy))
        screen.blit(text, text_rect)

    def _draw_solar_flare(self, screen: pygame.Surface, screen_x: int, screen_y: int):
        """Draw occasional solar flares."""
        flare_length = random.randint(30, 60)
        flare_angle = random.uniform(0, 2 * math.pi)

        end_x = screen_x + math.cos(flare_angle) * flare_length
        end_y = screen_y + math.sin(flare_angle) * flare_length

        # Draw flare as a thick line with gradient
        for i in range(5):
            width = 5 - i
            alpha = 255 - i * 40
            color = (255, 200 - i * 30, 100 - i * 20)

            if width > 0:
                pygame.draw.line(screen, color, (screen_x, screen_y), (int(end_x), int(end_y)), width)


class Planet(CelestialBody):
    """A planet that orbits around the Sun with enhanced visual effects."""

    def __init__(self, name: str, distance_from_sun: float, orbital_speed: float,
                 radius: float, color: Tuple[int, int, int],
                 initial_angle: float = 0.0, eccentricity: float = 0.0,
                 has_atmosphere: bool = False, atmosphere_color: Tuple[int, int, int] = None):
        """
        Initialize a planet.

        Args:
            name: Planet name
            distance_from_sun: Semi-major axis of orbit in AU
            orbital_speed: Angular velocity in radians per frame
            radius: Visual radius in pixels
            color: RGB color tuple
            initial_angle: Starting angle in radians
            eccentricity: Orbital eccentricity (0 = circular, <1 = elliptical)
            has_atmosphere: Whether planet has atmospheric effects
            atmosphere_color: Color of atmospheric glow
        """
        # Calculate initial position
        x = distance_from_sun * math.cos(initial_angle)
        y = distance_from_sun * math.sin(initial_angle)

        super().__init__(name, x, y, radius, color)

        self.distance_from_sun = distance_from_sun
        self.orbital_speed = orbital_speed
        self.angle = initial_angle
        self.eccentricity = eccentricity
        self.semi_major_axis = distance_from_sun
        self.semi_minor_axis = distance_from_sun * math.sqrt(1 - eccentricity**2)

        # Enhanced visual properties
        self.has_atmosphere = has_atmosphere
        self.atmosphere_color = atmosphere_color or color
        self.rotation_speed = orbital_speed * 10  # Planets rotate faster than they orbit
        self.surface_features = self._generate_surface_features()

        # Store information about the planet
        self.info = {
            'distance': f"{distance_from_sun/AU:.1f} AU",
            'period': f"{2*math.pi/orbital_speed:.0f} frames",
            'radius_km': f"{radius*1000:.0f} km (scaled)",
            'atmosphere': "Yes" if has_atmosphere else "No"
        }

    def _generate_surface_features(self):
        """Generate random surface features for visual variety."""
        features = []
        num_features = random.randint(3, 8)

        for _ in range(num_features):
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(0.3, 0.8) * self.radius
            size = random.uniform(0.1, 0.3) * self.radius
            brightness = random.uniform(0.7, 1.3)

            features.append({
                'angle': angle,
                'distance': distance,
                'size': size,
                'brightness': brightness
            })

        return features
    
    def update(self, sun_x: float, sun_y: float, dt: float = 1.0):
        """
        Update planet position and visual effects.

        Args:
            sun_x, sun_y: Position of the Sun
            dt: Time delta for animation speed control
        """
        # Update orbital angle
        self.angle += self.orbital_speed * dt

        # Update rotation for surface features
        self.rotation_angle += self.rotation_speed * dt

        # Calculate elliptical orbit position
        current_distance = self.semi_major_axis * (1 - self.eccentricity * math.cos(self.angle))

        # Calculate position relative to Sun
        self.x = sun_x + current_distance * math.cos(self.angle)
        self.y = sun_y + current_distance * math.sin(self.angle)

        # Update atmospheric glow based on distance from sun
        if self.has_atmosphere:
            distance_to_sun = self.get_distance_from_sun(sun_x, sun_y)
            max_distance = self.semi_major_axis * 2
            self.glow_intensity = max(0.1, 1.0 - (distance_to_sun / max_distance))

        # Update trail
        self.update_trail()

    def draw(self, screen: pygame.Surface, camera_offset: Tuple[float, float] = (0, 0)):
        """Draw planet with enhanced visual effects."""
        # Call parent draw method for basic rendering
        super().draw(screen, camera_offset)

        screen_x = int(self.x - camera_offset[0])
        screen_y = int(self.y - camera_offset[1])

        # Draw surface features
        self._draw_surface_features(screen, screen_x, screen_y)

        # Draw atmospheric effects
        if self.has_atmosphere:
            self._draw_atmosphere(screen, screen_x, screen_y)

    def _draw_surface_features(self, screen: pygame.Surface, screen_x: int, screen_y: int):
        """Draw rotating surface features on the planet."""
        for feature in self.surface_features:
            # Calculate feature position with rotation
            rotated_angle = feature['angle'] + self.rotation_angle
            feature_x = screen_x + math.cos(rotated_angle) * feature['distance']
            feature_y = screen_y + math.sin(rotated_angle) * feature['distance']

            # Only draw if feature is on the visible hemisphere
            if math.cos(rotated_angle) > 0:  # Front hemisphere
                feature_color = tuple(int(c * feature['brightness']) for c in self.color)
                feature_color = tuple(min(255, max(0, c)) for c in feature_color)

                pygame.draw.circle(screen, feature_color,
                                 (int(feature_x), int(feature_y)),
                                 int(feature['size']))

    def _draw_atmosphere(self, screen: pygame.Surface, screen_x: int, screen_y: int):
        """Draw atmospheric glow effect."""
        if self.glow_intensity > 0:
            atmo_radius = self.radius + 6
            atmo_alpha = int(self.glow_intensity * 80)

            # Create atmospheric glow
            atmo_surface = pygame.Surface((atmo_radius * 2, atmo_radius * 2), pygame.SRCALPHA)
            atmo_color = (*self.atmosphere_color, atmo_alpha)

            # Draw multiple layers for smooth gradient
            for i in range(3):
                layer_radius = atmo_radius - i * 2
                layer_alpha = atmo_alpha // (i + 1)
                layer_color = (*self.atmosphere_color, layer_alpha)

                if layer_radius > 0:
                    pygame.draw.circle(atmo_surface, layer_color,
                                     (atmo_radius, atmo_radius), layer_radius)

            screen.blit(atmo_surface, (screen_x - atmo_radius, screen_y - atmo_radius))
    
    def get_distance_from_sun(self, sun_x: float, sun_y: float) -> float:
        """Calculate current distance from the Sun."""
        return math.sqrt((self.x - sun_x)**2 + (self.y - sun_y)**2)


class SolarSystemSimulation:
    """Main simulation class that manages the enhanced solar system."""

    def __init__(self):
        """Initialize the solar system simulation."""
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Enhanced Solar System Simulation")
        self.clock = pygame.time.Clock()

        # Enhanced fonts
        try:
            self.title_font = pygame.font.Font(None, 48)
            self.font = pygame.font.Font(None, 32)
            self.small_font = pygame.font.Font(None, 20)
        except:
            self.title_font = pygame.font.Font(None, 36)
            self.font = pygame.font.Font(None, 24)
            self.small_font = pygame.font.Font(None, 18)

        # Simulation state
        self.running = True
        self.paused = False
        self.speed_multiplier = 1.0
        self.show_info = True  # Start with info visible
        self.show_trails = True
        self.selected_planet = None
        self.camera_offset = [0, 0]
        self.zoom_level = 1.0

        # Visual effects
        self.particle_system = ParticleSystem()
        self.background_stars = self._generate_background_stars()
        self.nebula_effects = self._generate_nebula_effects()

        # Create celestial bodies
        self.sun = Sun(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
        self.planets = self._create_planets()

        # UI elements
        self.ui_panel_height = 120
        self.show_fps = False

    def _generate_background_stars(self):
        """Generate beautiful background stars."""
        stars = []
        for _ in range(200):
            x = random.randint(0, SCREEN_WIDTH)
            y = random.randint(0, SCREEN_HEIGHT)
            brightness = random.uniform(0.3, 1.0)
            size = random.choice([1, 1, 1, 2, 2, 3])  # Mostly small stars
            twinkle_speed = random.uniform(0.02, 0.08)

            stars.append({
                'x': x, 'y': y, 'brightness': brightness,
                'size': size, 'twinkle_speed': twinkle_speed,
                'twinkle_phase': random.uniform(0, 2 * math.pi)
            })
        return stars

    def _generate_nebula_effects(self):
        """Generate subtle nebula background effects."""
        nebulae = []
        for _ in range(5):
            x = random.randint(-100, SCREEN_WIDTH + 100)
            y = random.randint(-100, SCREEN_HEIGHT + 100)
            size = random.randint(200, 400)
            color = random.choice([
                (100, 50, 150),   # Purple
                (50, 100, 150),   # Blue
                (150, 50, 100),   # Pink
                (100, 150, 50),   # Green
            ])
            alpha = random.randint(10, 30)

            nebulae.append({
                'x': x, 'y': y, 'size': size,
                'color': color, 'alpha': alpha
            })
        return nebulae

    def _create_planets(self) -> List[Planet]:
        """Create all planets with enhanced visual data."""
        # Enhanced planetary data: [name, distance_AU, orbital_speed, radius, color, initial_angle, eccentricity, has_atmosphere, atmosphere_color]
        planet_data = [
            ("Mercury", 0.4*AU, 0.08, 4, (169, 169, 169), 0, 0.21, False, None),
            ("Venus", 0.7*AU, 0.06, 5, (255, 198, 73), math.pi/4, 0.01, True, (255, 220, 100)),
            ("Earth", 1.0*AU, 0.05, 6, (100, 149, 237), math.pi/2, 0.02, True, (135, 206, 250)),
            ("Mars", 1.5*AU, 0.04, 5, (205, 92, 92), 3*math.pi/4, 0.09, True, (255, 160, 122)),
            ("Jupiter", 2.8*AU, 0.02, 15, (255, 140, 0), math.pi, 0.05, True, (255, 165, 79)),
            ("Saturn", 4.5*AU, 0.015, 12, (250, 230, 140), 5*math.pi/4, 0.06, True, (255, 248, 220)),
            ("Uranus", 6.5*AU, 0.01, 8, (64, 224, 208), 3*math.pi/2, 0.05, True, (175, 238, 238)),
            ("Neptune", 8.0*AU, 0.008, 8, (65, 105, 225), 7*math.pi/4, 0.01, True, (123, 104, 238))
        ]

        planets = []
        for name, distance, speed, radius, color, angle, eccentricity, has_atmo, atmo_color in planet_data:
            planet = Planet(name, distance, speed, radius, color, angle, eccentricity, has_atmo, atmo_color)
            planets.append(planet)

        return planets

    def handle_events(self):
        """Handle pygame events with enhanced controls."""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                    # Add particle effect when pausing/unpausing
                    if self.selected_planet:
                        self.particle_system.add_explosion(
                            self.selected_planet.x, self.selected_planet.y,
                            self.selected_planet.color, 10
                        )
                elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                    self.speed_multiplier = min(10.0, self.speed_multiplier + 0.5)
                elif event.key == pygame.K_MINUS:
                    self.speed_multiplier = max(0.1, self.speed_multiplier - 0.5)
                elif event.key == pygame.K_r:
                    self._reset_simulation()
                elif event.key == pygame.K_i:
                    self.show_info = not self.show_info
                elif event.key == pygame.K_t:
                    self.show_trails = not self.show_trails
                elif event.key == pygame.K_f:
                    self.show_fps = not self.show_fps
                elif event.key == pygame.K_ESCAPE:
                    self.selected_planet = None

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    self._handle_planet_selection(event.pos)
                elif event.button == 4:  # Mouse wheel up
                    self.zoom_level = min(3.0, self.zoom_level + 0.1)
                elif event.button == 5:  # Mouse wheel down
                    self.zoom_level = max(0.5, self.zoom_level - 0.1)

    def _handle_planet_selection(self, mouse_pos: Tuple[int, int]):
        """Handle planet selection on mouse click."""
        mouse_x, mouse_y = mouse_pos

        # Check if clicked on any planet
        for planet in self.planets:
            screen_x = int(planet.x - self.camera_offset[0])
            screen_y = int(planet.y - self.camera_offset[1])
            distance = math.sqrt((mouse_x - screen_x)**2 + (mouse_y - screen_y)**2)

            if distance <= planet.radius + 5:  # Small tolerance
                self.selected_planet = planet
                return

        # Check if clicked on Sun
        sun_screen_x = int(self.sun.x - self.camera_offset[0])
        sun_screen_y = int(self.sun.y - self.camera_offset[1])
        sun_distance = math.sqrt((mouse_x - sun_screen_x)**2 + (mouse_y - sun_screen_y)**2)

        if sun_distance <= self.sun.radius + 5:
            self.selected_planet = self.sun
        else:
            self.selected_planet = None

    def _reset_simulation(self):
        """Reset the simulation to initial state."""
        self.sun = Sun(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
        self.planets = self._create_planets()
        self.speed_multiplier = 1.0
        self.camera_offset = [0, 0]
        self.zoom_level = 1.0
        self.selected_planet = None

    def update(self):
        """Update simulation physics and effects."""
        if not self.paused:
            # Update sun effects
            self.sun.update(self.speed_multiplier)

            # Update planet positions
            for planet in self.planets:
                planet.update(self.sun.x, self.sun.y, self.speed_multiplier)

            # Add solar wind particles
            if random.random() < 0.3:  # 30% chance per frame
                self.particle_system.add_solar_wind(self.sun.x, self.sun.y)

        # Always update particle system (even when paused for visual continuity)
        self.particle_system.update(self.speed_multiplier if not self.paused else 0.1)

        # Update twinkling stars
        for star in self.background_stars:
            star['twinkle_phase'] += star['twinkle_speed']

    def draw(self):
        """Render the enhanced simulation."""
        # Create gradient background
        self._draw_gradient_background()

        # Draw nebula effects
        self._draw_nebula_effects()

        # Draw twinkling stars
        self._draw_enhanced_stars()

        # Draw particle effects (behind celestial bodies)
        self.particle_system.draw(self.screen, self.camera_offset)

        # Draw Sun with enhanced effects
        self.sun.draw(self.screen, self.camera_offset)

        # Draw planets with enhanced visuals
        for planet in self.planets:
            if self.show_trails or planet == self.selected_planet:
                planet.draw(self.screen, self.camera_offset)
            else:
                # Draw without trails for performance
                screen_x = int(planet.x - self.camera_offset[0])
                screen_y = int(planet.y - self.camera_offset[1])
                planet._draw_enhanced_body(self.screen, screen_x, screen_y)
                planet._draw_enhanced_label(self.screen, screen_x, screen_y)

        # Highlight selected planet
        if self.selected_planet:
            self._draw_selection_highlight()

        # Draw enhanced UI
        self._draw_enhanced_ui()

        # Draw planet information if selected
        if self.selected_planet and self.show_info:
            self._draw_enhanced_planet_info()

        # Draw FPS if enabled
        if self.show_fps:
            self._draw_fps()

        pygame.display.flip()

    def _draw_gradient_background(self):
        """Draw a beautiful gradient background."""
        # Create vertical gradient from dark blue to black
        for y in range(SCREEN_HEIGHT):
            ratio = y / SCREEN_HEIGHT
            r = int(BACKGROUND_COLOR[0] * (1 - ratio) + 2 * ratio)
            g = int(BACKGROUND_COLOR[1] * (1 - ratio) + 2 * ratio)
            b = int(BACKGROUND_COLOR[2] * (1 - ratio) + 15 * ratio)
            color = (r, g, b)
            pygame.draw.line(self.screen, color, (0, y), (SCREEN_WIDTH, y))

    def _draw_nebula_effects(self):
        """Draw subtle nebula background effects."""
        for nebula in self.nebula_effects:
            nebula_surface = pygame.Surface((nebula['size'], nebula['size']), pygame.SRCALPHA)
            color_with_alpha = (*nebula['color'], nebula['alpha'])

            # Draw multiple circles for smooth gradient
            for i in range(5):
                radius = nebula['size'] // 2 - i * 20
                alpha = nebula['alpha'] // (i + 1)
                if radius > 0:
                    circle_color = (*nebula['color'], alpha)
                    pygame.draw.circle(nebula_surface, circle_color,
                                     (nebula['size'] // 2, nebula['size'] // 2), radius)

            self.screen.blit(nebula_surface, (nebula['x'] - nebula['size'] // 2,
                                            nebula['y'] - nebula['size'] // 2))

    def _draw_enhanced_stars(self):
        """Draw beautiful twinkling stars."""
        for star in self.background_stars:
            # Calculate twinkling effect
            twinkle = 0.7 + 0.3 * math.sin(star['twinkle_phase'])
            brightness = int(255 * star['brightness'] * twinkle)
            color = (brightness, brightness, brightness)

            # Draw star with size variation
            if star['size'] == 1:
                pygame.draw.circle(self.screen, color, (star['x'], star['y']), 1)
            elif star['size'] == 2:
                # Draw cross pattern for medium stars
                pygame.draw.circle(self.screen, color, (star['x'], star['y']), 1)
                pygame.draw.line(self.screen, color,
                               (star['x'] - 2, star['y']), (star['x'] + 2, star['y']), 1)
                pygame.draw.line(self.screen, color,
                               (star['x'], star['y'] - 2), (star['x'], star['y'] + 2), 1)
            else:
                # Draw larger star with glow
                pygame.draw.circle(self.screen, color, (star['x'], star['y']), 2)
                glow_color = (brightness // 2, brightness // 2, brightness // 2)
                pygame.draw.circle(self.screen, glow_color, (star['x'], star['y']), 3)

    def _draw_selection_highlight(self):
        """Draw highlight around selected planet."""
        if not self.selected_planet:
            return

        screen_x = int(self.selected_planet.x - self.camera_offset[0])
        screen_y = int(self.selected_planet.y - self.camera_offset[1])

        # Pulsing highlight ring
        pulse = 1.0 + 0.3 * math.sin(pygame.time.get_ticks() * 0.01)
        highlight_radius = int((self.selected_planet.radius + 10) * pulse)

        # Draw multiple rings for glow effect
        for i in range(3):
            ring_radius = highlight_radius - i * 2
            alpha = 255 - i * 60
            color = (255, 255, 0, alpha)

            if ring_radius > 0:
                # Create surface for alpha blending
                ring_surface = pygame.Surface((ring_radius * 2, ring_radius * 2), pygame.SRCALPHA)
                pygame.draw.circle(ring_surface, color, (ring_radius, ring_radius), ring_radius, 2)
                self.screen.blit(ring_surface, (screen_x - ring_radius, screen_y - ring_radius))

    def _draw_enhanced_ui(self):
        """Draw enhanced user interface with modern styling."""
        # Create gradient background for UI panel
        ui_rect = pygame.Rect(0, SCREEN_HEIGHT - self.ui_panel_height, SCREEN_WIDTH, self.ui_panel_height)

        # Draw gradient background
        for y in range(self.ui_panel_height):
            ratio = y / self.ui_panel_height
            alpha = int(180 * (1 - ratio * 0.5))
            color = (25, 25, 45, alpha)
            line_surface = pygame.Surface((SCREEN_WIDTH, 1), pygame.SRCALPHA)
            line_surface.fill(color)
            self.screen.blit(line_surface, (0, SCREEN_HEIGHT - self.ui_panel_height + y))

        # Draw border
        pygame.draw.line(self.screen, COLORS['accent_blue'],
                        (0, SCREEN_HEIGHT - self.ui_panel_height),
                        (SCREEN_WIDTH, SCREEN_HEIGHT - self.ui_panel_height), 3)

        # Enhanced control instructions with icons
        controls = [
            ("⏸️ SPACE", "Pause/Resume"),
            ("⚡ +/-", "Speed Control"),
            ("ℹ️ I", "Toggle Info"),
            ("🔄 R", "Reset"),
            ("🖱️ Click", "Select Planet"),
            ("👁️ T", "Toggle Trails"),
            ("📊 F", "Show FPS")
        ]

        y_offset = SCREEN_HEIGHT - self.ui_panel_height + 15
        col_width = SCREEN_WIDTH // len(controls)

        for i, (key, desc) in enumerate(controls):
            x_pos = 10 + i * col_width

            # Draw key
            key_surface = self.small_font.render(key, True, COLORS['accent_gold'])
            self.screen.blit(key_surface, (x_pos, y_offset))

            # Draw description
            desc_surface = self.small_font.render(desc, True, WHITE)
            self.screen.blit(desc_surface, (x_pos, y_offset + 20))

        # Enhanced status information with styling
        status_y = y_offset + 50

        # Speed indicator with color coding
        speed_color = WHITE
        if self.speed_multiplier > 2.0:
            speed_color = (255, 100, 100)  # Red for fast
        elif self.speed_multiplier < 0.5:
            speed_color = (100, 100, 255)  # Blue for slow

        status_text = f"⚡ Speed: {self.speed_multiplier:.1f}x"
        if self.paused:
            status_text += " ⏸️ PAUSED"
            speed_color = YELLOW

        status_surface = self.font.render(status_text, True, speed_color)
        self.screen.blit(status_surface, (20, status_y))

        # Selected planet indicator with enhanced styling
        if self.selected_planet:
            selected_text = f"🎯 Selected: {self.selected_planet.name}"
            selected_surface = self.font.render(selected_text, True, COLORS['accent_gold'])

            # Draw background for selected text
            text_rect = selected_surface.get_rect()
            text_rect.x = SCREEN_WIDTH - text_rect.width - 20
            text_rect.y = status_y

            bg_rect = text_rect.inflate(10, 5)
            bg_surface = pygame.Surface(bg_rect.size, pygame.SRCALPHA)
            pygame.draw.rect(bg_surface, (50, 50, 100, 150), bg_surface.get_rect(), border_radius=5)
            self.screen.blit(bg_surface, bg_rect)

            self.screen.blit(selected_surface, text_rect)

        # Particle count indicator (for debugging/interest)
        particle_text = f"✨ Particles: {len(self.particle_system.particles)}"
        particle_surface = self.small_font.render(particle_text, True, COLORS['accent_blue'])
        self.screen.blit(particle_surface, (20, status_y + 30))

    def _draw_enhanced_planet_info(self):
        """Draw beautiful detailed information about the selected planet."""
        if not self.selected_planet:
            return

        # Enhanced info panel
        panel_width = 350
        panel_height = 200
        panel_x = SCREEN_WIDTH - panel_width - 20
        panel_y = 20

        # Create gradient background
        info_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)

        # Draw gradient background
        for y in range(panel_height):
            ratio = y / panel_height
            alpha = int(200 * (1 - ratio * 0.3))
            color = (30, 30, 60, alpha)
            line_surface = pygame.Surface((panel_width, 1), pygame.SRCALPHA)
            line_surface.fill(color)
            self.screen.blit(line_surface, (panel_x, panel_y + y))

        # Draw border with planet color
        border_color = self.selected_planet.color
        pygame.draw.rect(self.screen, border_color, info_rect, 3, border_radius=10)

        # Planet name with enhanced styling
        name_text = self.title_font.render(self.selected_planet.name, True, COLORS['accent_gold'])
        name_rect = name_text.get_rect()
        name_rect.centerx = panel_x + panel_width // 2
        name_rect.y = panel_y + 15
        self.screen.blit(name_text, name_rect)

        # Draw planet color indicator
        color_rect = pygame.Rect(panel_x + 15, panel_y + 15, 20, 20)
        pygame.draw.rect(self.screen, self.selected_planet.color, color_rect)
        pygame.draw.rect(self.screen, WHITE, color_rect, 2)

        # Enhanced planet information
        if hasattr(self.selected_planet, 'info'):
            y_offset = panel_y + 60
            for key, value in self.selected_planet.info.items():
                # Icon mapping
                icons = {
                    'distance': '🌍',
                    'period': '⏰',
                    'radius_km': '📏',
                    'atmosphere': '🌫️'
                }

                icon = icons.get(key, '•')
                info_text = f"{icon} {key.title()}: {value}"
                info_surface = self.font.render(info_text, True, WHITE)
                self.screen.blit(info_surface, (panel_x + 15, y_offset))
                y_offset += 30

        # Current distance from Sun (for planets)
        if isinstance(self.selected_planet, Planet):
            current_distance = self.selected_planet.get_distance_from_sun(self.sun.x, self.sun.y)
            distance_text = f"📍 Current: {current_distance/AU:.2f} AU"
            distance_surface = self.font.render(distance_text, True, COLORS['accent_blue'])
            self.screen.blit(distance_surface, (panel_x + 15, panel_y + 160))

    def _draw_fps(self):
        """Draw FPS counter."""
        fps = self.clock.get_fps()
        fps_text = f"FPS: {fps:.1f}"

        # Color code FPS
        if fps >= 50:
            fps_color = (0, 255, 0)  # Green
        elif fps >= 30:
            fps_color = (255, 255, 0)  # Yellow
        else:
            fps_color = (255, 0, 0)  # Red

        fps_surface = self.font.render(fps_text, True, fps_color)

        # Draw background
        fps_rect = fps_surface.get_rect()
        fps_rect.x = SCREEN_WIDTH - fps_rect.width - 20
        fps_rect.y = 20

        bg_rect = fps_rect.inflate(10, 5)
        bg_surface = pygame.Surface(bg_rect.size, pygame.SRCALPHA)
        pygame.draw.rect(bg_surface, (0, 0, 0, 150), bg_surface.get_rect(), border_radius=5)
        self.screen.blit(bg_surface, bg_rect)

        self.screen.blit(fps_surface, fps_rect)

    def run(self):
        """Main enhanced simulation loop."""
        print("🌟 Enhanced Solar System Simulation Started! 🌟")
        print("=" * 50)
        print("🎮 Controls:")
        print("  ⏸️  SPACE - Pause/Resume")
        print("  ⚡ +/- - Adjust speed (0.1x to 10x)")
        print("  ℹ️  I - Toggle planet information")
        print("  👁️  T - Toggle orbital trails")
        print("  📊 F - Toggle FPS display")
        print("  🔄 R - Reset simulation")
        print("  🖱️  Click on planets to select them")
        print("  🚪 ESC - Deselect planet")
        print("=" * 50)
        print("✨ Features:")
        print("  • Enhanced visual effects with particles")
        print("  • Realistic orbital mechanics")
        print("  • Atmospheric effects on planets")
        print("  • Beautiful star field background")
        print("  • Interactive planet selection")
        print("=" * 50)

        while self.running:
            self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(FPS)

        print("👋 Thanks for exploring the solar system!")
        pygame.quit()
        sys.exit()


def main():
    """Main function to start the enhanced solar system simulation."""
    print("🚀 Initializing Enhanced Solar System Simulation...")

    try:
        # Check pygame version
        print(f"🎮 Pygame version: {pygame.version.ver}")
        print(f"📺 Screen resolution: {SCREEN_WIDTH}x{SCREEN_HEIGHT}")
        print(f"⚡ Target FPS: {FPS}")
        print()

        simulation = SolarSystemSimulation()
        simulation.run()

    except KeyboardInterrupt:
        print("\n⏹️  Simulation interrupted by user")
    except Exception as e:
        print(f"❌ Error running simulation: {e}")
        import traceback
        traceback.print_exc()
    finally:
        pygame.quit()
        sys.exit()


if __name__ == "__main__":
    main()
