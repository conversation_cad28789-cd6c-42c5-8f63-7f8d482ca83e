# Solar System Simulation

A comprehensive 2D solar system simulation built with Python and Pygame, featuring realistic orbital mechanics and interactive controls.

## Features

### Visual Representation
- **Realistic celestial bodies**: Sun at center with 8 major planets (Mercury through Neptune)
- **Accurate relative sizes and colors**: Each planet has appropriate visual representation
- **Orbital trails**: Beautiful trailing effects showing planetary paths
- **Starfield background**: Immersive space environment

### Orbital Mechanics
- **Realistic motion**: Planets orbit based on scaled astronomical data
- **Elliptical orbits**: Includes orbital eccentricity for realistic paths
- **Variable speeds**: Inner planets move faster than outer planets (Kepler's laws)
- **Smooth animation**: 60 FPS for fluid motion

### Interactive Features
- **Pause/Resume**: Space bar to control simulation
- **Speed control**: +/- keys to adjust simulation speed (0.1x to 5.0x)
- **Planet selection**: Click on any celestial body for detailed information
- **Information display**: Toggle detailed planet data with 'I' key
- **Reset function**: 'R' key to restart simulation
- **Real-time data**: Current distance from Sun and orbital information

## Installation

1. **Install Python 3.7+** (if not already installed)

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the simulation**:
   ```bash
   python solar_system.py
   ```

## Controls

| Key | Action |
|-----|--------|
| `SPACE` | Pause/Resume simulation |
| `+` / `=` | Increase simulation speed |
| `-` | Decrease simulation speed |
| `I` | Toggle planet information display |
| `R` | Reset simulation to initial state |
| `ESC` | Deselect currently selected planet |
| `Mouse Click` | Select planet or Sun for information |

## Technical Details

### Object-Oriented Design
- **CelestialBody**: Base class for all space objects
- **Sun**: Special star class with glowing effects
- **Planet**: Orbital mechanics and physics calculations
- **SolarSystemSimulation**: Main simulation engine

### Physics Implementation
- **Scaled distances**: 1 AU = 100 pixels for visibility
- **Orbital mechanics**: Based on Kepler's laws of planetary motion
- **Elliptical orbits**: Configurable eccentricity for each planet
- **Real-time calculations**: Smooth position updates at 60 FPS

### Planetary Data
All planets include realistic (scaled) data:
- **Orbital distances**: Based on actual AU measurements
- **Orbital speeds**: Proportional to real planetary periods
- **Visual appearance**: Appropriate colors and relative sizes
- **Eccentricity**: Real orbital ellipse parameters

## Educational Value

This simulation demonstrates:
- **Kepler's Laws**: Planetary motion and orbital mechanics
- **Scale relationships**: Relative sizes and distances in the solar system
- **Orbital dynamics**: How gravity affects planetary motion
- **Programming concepts**: Object-oriented design, game loops, event handling

## Customization

The simulation can be easily modified:
- **Add moons**: Extend Planet class for satellite objects
- **Modify physics**: Adjust gravitational constants or orbital parameters
- **Visual enhancements**: Add textures, better graphics, or 3D effects
- **New features**: Implement asteroid belts, comets, or spacecraft

## Requirements

- Python 3.7 or higher
- Pygame 2.5.0 or higher
- Approximately 1200x800 screen resolution recommended

## Author

Created as a comprehensive educational tool for understanding solar system mechanics and Python game development.
