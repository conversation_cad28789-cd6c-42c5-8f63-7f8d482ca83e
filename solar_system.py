#!/usr/bin/env python3
"""
Solar System Simulation
A comprehensive 2D solar system simulation using pygame with realistic orbital mechanics.

Features:
- Object-oriented design with celestial body classes
- Realistic orbital motion based on <PERSON><PERSON>'s laws
- Interactive controls (pause, speed adjustment, planet information)
- Visual enhancements (trails, labels, scaling)
- Smooth 60 FPS animation

Author: AI Assistant
Date: 2025-08-05
"""

import pygame
import math
import sys
from typing import List, Tuple, Optional

# Initialize pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60
BACKGROUND_COLOR = (5, 5, 15)  # Dark space blue
WHITE = (255, 255, 255)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)

# Physics constants (scaled for visualization)
AU = 100  # Astronomical Unit in pixels (scaled down from real 149.6M km)
GRAVITATIONAL_CONSTANT = 1.0  # Simplified for simulation
SUN_MASS = 1000  # Arbitrary units for simulation


class CelestialBody:
    """Base class for all celestial objects in the solar system."""
    
    def __init__(self, name: str, x: float, y: float, radius: float, 
                 color: Tuple[int, int, int], mass: float = 1.0):
        """
        Initialize a celestial body.
        
        Args:
            name: Name of the celestial body
            x, y: Initial position coordinates
            radius: Visual radius in pixels
            color: RGB color tuple
            mass: Mass for gravitational calculations
        """
        self.name = name
        self.x = x
        self.y = y
        self.radius = radius
        self.color = color
        self.mass = mass
        self.trail = []  # Store previous positions for orbital trails
        self.max_trail_length = 200
        
    def draw(self, screen: pygame.Surface, camera_offset: Tuple[float, float] = (0, 0)):
        """Draw the celestial body on the screen."""
        screen_x = int(self.x - camera_offset[0])
        screen_y = int(self.y - camera_offset[1])
        
        # Draw orbital trail
        if len(self.trail) > 1:
            trail_points = []
            for i, (tx, ty) in enumerate(self.trail):
                trail_x = int(tx - camera_offset[0])
                trail_y = int(ty - camera_offset[1])
                if 0 <= trail_x <= SCREEN_WIDTH and 0 <= trail_y <= SCREEN_HEIGHT:
                    trail_points.append((trail_x, trail_y))
            
            if len(trail_points) > 1:
                # Draw trail with fading effect
                for i in range(1, len(trail_points)):
                    alpha = int(255 * (i / len(trail_points)) * 0.3)
                    trail_color = (*self.color, alpha)
                    if i < len(trail_points) - 1:
                        pygame.draw.line(screen, self.color, trail_points[i-1], trail_points[i], 1)
        
        # Draw the celestial body
        pygame.draw.circle(screen, self.color, (screen_x, screen_y), self.radius)
        
        # Draw name label
        font = pygame.font.Font(None, 20)
        text = font.render(self.name, True, WHITE)
        text_rect = text.get_rect(center=(screen_x, screen_y + self.radius + 15))
        screen.blit(text, text_rect)
    
    def update_trail(self):
        """Update the orbital trail."""
        self.trail.append((self.x, self.y))
        if len(self.trail) > self.max_trail_length:
            self.trail.pop(0)


class Sun(CelestialBody):
    """The Sun - central star of the solar system."""
    
    def __init__(self, x: float, y: float):
        super().__init__("Sun", x, y, radius=20, color=YELLOW, mass=SUN_MASS)
        self.is_star = True
    
    def draw(self, screen: pygame.Surface, camera_offset: Tuple[float, float] = (0, 0)):
        """Draw the Sun with a glowing effect."""
        screen_x = int(self.x - camera_offset[0])
        screen_y = int(self.y - camera_offset[1])
        
        # Draw glow effect
        for i in range(5):
            glow_radius = self.radius + i * 3
            glow_alpha = max(0, 100 - i * 20)
            glow_color = (255, 255, 100, glow_alpha)
            # Create a surface for the glow effect
            glow_surface = pygame.Surface((glow_radius * 2, glow_radius * 2), pygame.SRCALPHA)
            pygame.draw.circle(glow_surface, glow_color, (glow_radius, glow_radius), glow_radius)
            screen.blit(glow_surface, (screen_x - glow_radius, screen_y - glow_radius))
        
        # Draw the Sun itself
        pygame.draw.circle(screen, self.color, (screen_x, screen_y), self.radius)
        
        # Draw name label
        font = pygame.font.Font(None, 24)
        text = font.render(self.name, True, WHITE)
        text_rect = text.get_rect(center=(screen_x, screen_y + self.radius + 20))
        screen.blit(text, text_rect)


class Planet(CelestialBody):
    """A planet that orbits around the Sun."""
    
    def __init__(self, name: str, distance_from_sun: float, orbital_speed: float,
                 radius: float, color: Tuple[int, int, int], 
                 initial_angle: float = 0.0, eccentricity: float = 0.0):
        """
        Initialize a planet.
        
        Args:
            name: Planet name
            distance_from_sun: Semi-major axis of orbit in AU
            orbital_speed: Angular velocity in radians per frame
            radius: Visual radius in pixels
            color: RGB color tuple
            initial_angle: Starting angle in radians
            eccentricity: Orbital eccentricity (0 = circular, <1 = elliptical)
        """
        # Calculate initial position
        x = distance_from_sun * math.cos(initial_angle)
        y = distance_from_sun * math.sin(initial_angle)
        
        super().__init__(name, x, y, radius, color)
        
        self.distance_from_sun = distance_from_sun
        self.orbital_speed = orbital_speed
        self.angle = initial_angle
        self.eccentricity = eccentricity
        self.semi_major_axis = distance_from_sun
        self.semi_minor_axis = distance_from_sun * math.sqrt(1 - eccentricity**2)
        
        # Store information about the planet
        self.info = {
            'distance': f"{distance_from_sun/AU:.1f} AU",
            'period': f"{2*math.pi/orbital_speed:.0f} frames",
            'radius_km': f"{radius*1000:.0f} km (scaled)"
        }
    
    def update(self, sun_x: float, sun_y: float, dt: float = 1.0):
        """
        Update planet position based on orbital mechanics.
        
        Args:
            sun_x, sun_y: Position of the Sun
            dt: Time delta for animation speed control
        """
        # Update orbital angle
        self.angle += self.orbital_speed * dt
        
        # Calculate elliptical orbit position
        # For simplicity, we'll use a circular orbit with slight eccentricity
        current_distance = self.semi_major_axis * (1 - self.eccentricity * math.cos(self.angle))
        
        # Calculate position relative to Sun
        self.x = sun_x + current_distance * math.cos(self.angle)
        self.y = sun_y + current_distance * math.sin(self.angle)
        
        # Update trail
        self.update_trail()
    
    def get_distance_from_sun(self, sun_x: float, sun_y: float) -> float:
        """Calculate current distance from the Sun."""
        return math.sqrt((self.x - sun_x)**2 + (self.y - sun_y)**2)


class SolarSystemSimulation:
    """Main simulation class that manages the solar system."""

    def __init__(self):
        """Initialize the solar system simulation."""
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Solar System Simulation")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)

        # Simulation state
        self.running = True
        self.paused = False
        self.speed_multiplier = 1.0
        self.show_info = False
        self.selected_planet = None
        self.camera_offset = [0, 0]
        self.zoom_level = 1.0

        # Create celestial bodies
        self.sun = Sun(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
        self.planets = self._create_planets()

        # UI elements
        self.ui_panel_height = 100

    def _create_planets(self) -> List[Planet]:
        """Create all planets with realistic data (scaled for visualization)."""
        # Planetary data: [name, distance_AU, orbital_speed, radius, color, initial_angle, eccentricity]
        planet_data = [
            ("Mercury", 0.4*AU, 0.08, 3, (169, 169, 169), 0, 0.21),      # Gray
            ("Venus", 0.7*AU, 0.06, 4, (255, 198, 73), math.pi/4, 0.01), # Yellow-orange
            ("Earth", 1.0*AU, 0.05, 5, (100, 149, 237), math.pi/2, 0.02), # Blue
            ("Mars", 1.5*AU, 0.04, 4, (205, 92, 92), 3*math.pi/4, 0.09),  # Red
            ("Jupiter", 2.8*AU, 0.02, 12, (255, 140, 0), math.pi, 0.05),   # Orange
            ("Saturn", 4.5*AU, 0.015, 10, (250, 230, 140), 5*math.pi/4, 0.06), # Pale yellow
            ("Uranus", 6.5*AU, 0.01, 7, (64, 224, 208), 3*math.pi/2, 0.05),    # Cyan
            ("Neptune", 8.0*AU, 0.008, 7, (65, 105, 225), 7*math.pi/4, 0.01)   # Blue
        ]

        planets = []
        for name, distance, speed, radius, color, angle, eccentricity in planet_data:
            planet = Planet(name, distance, speed, radius, color, angle, eccentricity)
            planets.append(planet)

        return planets

    def handle_events(self):
        """Handle pygame events."""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                elif event.key == pygame.K_PLUS or event.key == pygame.K_EQUALS:
                    self.speed_multiplier = min(5.0, self.speed_multiplier + 0.5)
                elif event.key == pygame.K_MINUS:
                    self.speed_multiplier = max(0.1, self.speed_multiplier - 0.5)
                elif event.key == pygame.K_r:
                    self._reset_simulation()
                elif event.key == pygame.K_i:
                    self.show_info = not self.show_info
                elif event.key == pygame.K_ESCAPE:
                    self.selected_planet = None

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    self._handle_planet_selection(event.pos)
                elif event.button == 4:  # Mouse wheel up
                    self.zoom_level = min(3.0, self.zoom_level + 0.1)
                elif event.button == 5:  # Mouse wheel down
                    self.zoom_level = max(0.5, self.zoom_level - 0.1)

    def _handle_planet_selection(self, mouse_pos: Tuple[int, int]):
        """Handle planet selection on mouse click."""
        mouse_x, mouse_y = mouse_pos

        # Check if clicked on any planet
        for planet in self.planets:
            screen_x = int(planet.x - self.camera_offset[0])
            screen_y = int(planet.y - self.camera_offset[1])
            distance = math.sqrt((mouse_x - screen_x)**2 + (mouse_y - screen_y)**2)

            if distance <= planet.radius + 5:  # Small tolerance
                self.selected_planet = planet
                return

        # Check if clicked on Sun
        sun_screen_x = int(self.sun.x - self.camera_offset[0])
        sun_screen_y = int(self.sun.y - self.camera_offset[1])
        sun_distance = math.sqrt((mouse_x - sun_screen_x)**2 + (mouse_y - sun_screen_y)**2)

        if sun_distance <= self.sun.radius + 5:
            self.selected_planet = self.sun
        else:
            self.selected_planet = None

    def _reset_simulation(self):
        """Reset the simulation to initial state."""
        self.sun = Sun(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)
        self.planets = self._create_planets()
        self.speed_multiplier = 1.0
        self.camera_offset = [0, 0]
        self.zoom_level = 1.0
        self.selected_planet = None

    def update(self):
        """Update simulation physics."""
        if not self.paused:
            # Update planet positions
            for planet in self.planets:
                planet.update(self.sun.x, self.sun.y, self.speed_multiplier)

    def draw(self):
        """Render the simulation."""
        self.screen.fill(BACKGROUND_COLOR)

        # Draw stars background
        self._draw_stars()

        # Draw Sun
        self.sun.draw(self.screen, self.camera_offset)

        # Draw planets
        for planet in self.planets:
            planet.draw(self.screen, self.camera_offset)

        # Draw UI
        self._draw_ui()

        # Draw planet information if selected
        if self.selected_planet and self.show_info:
            self._draw_planet_info()

        pygame.display.flip()

    def _draw_stars(self):
        """Draw background stars for visual appeal."""
        import random
        random.seed(42)  # Fixed seed for consistent star positions

        for _ in range(100):
            x = random.randint(0, SCREEN_WIDTH)
            y = random.randint(0, SCREEN_HEIGHT)
            brightness = random.randint(50, 200)
            color = (brightness, brightness, brightness)
            pygame.draw.circle(self.screen, color, (x, y), 1)

    def _draw_ui(self):
        """Draw user interface elements."""
        # Control panel background
        ui_rect = pygame.Rect(0, SCREEN_HEIGHT - self.ui_panel_height, SCREEN_WIDTH, self.ui_panel_height)
        pygame.draw.rect(self.screen, (20, 20, 40), ui_rect)
        pygame.draw.line(self.screen, WHITE, (0, SCREEN_HEIGHT - self.ui_panel_height),
                        (SCREEN_WIDTH, SCREEN_HEIGHT - self.ui_panel_height), 2)

        # Control instructions
        controls = [
            "SPACE: Pause/Resume",
            "+/-: Speed Control",
            "I: Toggle Info",
            "R: Reset",
            "Click: Select Planet"
        ]

        y_offset = SCREEN_HEIGHT - self.ui_panel_height + 10
        for i, control in enumerate(controls):
            text = self.small_font.render(control, True, WHITE)
            self.screen.blit(text, (10 + (i * 200), y_offset))

        # Status information
        status_y = y_offset + 25
        status_text = f"Speed: {self.speed_multiplier:.1f}x"
        if self.paused:
            status_text += " (PAUSED)"

        status_surface = self.small_font.render(status_text, True, WHITE)
        self.screen.blit(status_surface, (10, status_y))

        # Selected planet indicator
        if self.selected_planet:
            selected_text = f"Selected: {self.selected_planet.name}"
            selected_surface = self.small_font.render(selected_text, True, YELLOW)
            self.screen.blit(selected_surface, (10, status_y + 20))

    def _draw_planet_info(self):
        """Draw detailed information about the selected planet."""
        if not self.selected_planet:
            return

        # Info panel background
        panel_width = 300
        panel_height = 150
        panel_x = SCREEN_WIDTH - panel_width - 10
        panel_y = 10

        info_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(self.screen, (40, 40, 60), info_rect)
        pygame.draw.rect(self.screen, WHITE, info_rect, 2)

        # Planet name
        name_text = self.font.render(self.selected_planet.name, True, WHITE)
        self.screen.blit(name_text, (panel_x + 10, panel_y + 10))

        # Planet information
        if hasattr(self.selected_planet, 'info'):
            y_offset = panel_y + 50
            for key, value in self.selected_planet.info.items():
                info_text = f"{key.title()}: {value}"
                info_surface = self.small_font.render(info_text, True, WHITE)
                self.screen.blit(info_surface, (panel_x + 10, y_offset))
                y_offset += 25

        # Current distance from Sun (for planets)
        if isinstance(self.selected_planet, Planet):
            current_distance = self.selected_planet.get_distance_from_sun(self.sun.x, self.sun.y)
            distance_text = f"Current distance: {current_distance/AU:.2f} AU"
            distance_surface = self.small_font.render(distance_text, True, WHITE)
            self.screen.blit(distance_surface, (panel_x + 10, panel_y + 120))

    def run(self):
        """Main simulation loop."""
        print("Solar System Simulation Started!")
        print("Controls:")
        print("  SPACE - Pause/Resume")
        print("  +/- - Adjust speed")
        print("  I - Toggle planet information")
        print("  R - Reset simulation")
        print("  Click on planets to select them")
        print("  ESC - Deselect planet")

        while self.running:
            self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(FPS)

        pygame.quit()
        sys.exit()


def main():
    """Main function to start the solar system simulation."""
    try:
        simulation = SolarSystemSimulation()
        simulation.run()
    except Exception as e:
        print(f"Error running simulation: {e}")
        pygame.quit()
        sys.exit(1)


if __name__ == "__main__":
    main()
