# Installation Guide

## Quick Start

1. **Install pygame**:
   ```bash
   pip install pygame
   ```

2. **Run the simulation**:
   ```bash
   python solar_system.py
   ```

## Detailed Installation

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Step-by-Step Installation

1. **Check Python version**:
   ```bash
   python --version
   ```
   Make sure you have Python 3.7+

2. **Install pygame using pip**:
   ```bash
   pip install pygame
   ```

3. **Verify installation**:
   ```bash
   python -c "import pygame; print('Pygame version:', pygame.version.ver)"
   ```

4. **Run tests** (optional):
   ```bash
   python test_simulation.py
   ```

5. **Start the simulation**:
   ```bash
   python solar_system.py
   ```

### Alternative Installation Methods

#### Using virtual environment (recommended):
```bash
# Create virtual environment
python -m venv solar_system_env

# Activate virtual environment
# On Windows:
solar_system_env\Scripts\activate
# On macOS/Linux:
source solar_system_env/bin/activate

# Install pygame
pip install pygame

# Run simulation
python solar_system.py
```

#### Using requirements.txt:
```bash
pip install -r requirements.txt
python solar_system.py
```

## Troubleshooting

### Common Issues

1. **"No module named 'pygame'"**
   - Solution: Install pygame with `pip install pygame`

2. **"Python command not found"**
   - Solution: Make sure Python is installed and added to PATH

3. **Permission errors on macOS/Linux**
   - Solution: Try `python3` instead of `python`
   - Or use `pip3` instead of `pip`

4. **Pygame installation fails**
   - Solution: Update pip first: `pip install --upgrade pip`
   - Then try: `pip install pygame`

### System-Specific Notes

#### Windows:
- Use Command Prompt or PowerShell
- May need to use `py` instead of `python`

#### macOS:
- May need to use `python3` and `pip3`
- If using Homebrew Python, ensure it's in PATH

#### Linux:
- May need to install additional dependencies:
  ```bash
  sudo apt-get install python3-pygame  # Ubuntu/Debian
  ```

## Performance Tips

- Close other applications for better performance
- Use a dedicated graphics card if available
- Ensure adequate RAM (minimum 4GB recommended)

## Next Steps

Once installed, check out:
- `README.md` for detailed features and controls
- `solar_system.py` for the main simulation code
- Experiment with different speed settings and planet selections!
